import numpy as np

def analyze_returns(data):
    """Calculates daily returns, average return, and volatility."""
    data['Daily Return'] = data['Close'].pct_change()
    avg_return = np.mean(data['Daily Return']) * 100
    volatility = np.std(data['Daily Return']) * 100
    max_return = np.max(data['Daily Return']) * 100
    min_return = np.min(data['Daily Return']) * 100

    print(f"平均收益率: {avg_return:.2f}%")
    print(f"波动率(风险): {volatility:.2f}%")
    print(f"最大单日涨幅: {max_return:.2f}%")
    print(f"最大单日跌幅: {min_return:.2f}%")

    # 保存结果
    with open("result.txt", "w") as f:
        f.write(f"平均收益率: {avg_return:.2f}%\n")
        f.write(f"波动率(风险): {volatility:.2f}%\n")
        f.write(f"最大涨幅: {max_return:.2f}%\n")
        f.write(f"最大跌幅: {min_return:.2f}%\n")

    return data
