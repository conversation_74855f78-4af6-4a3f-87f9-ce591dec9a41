# fetch_data.py
import yfinance as yf
import pandas as pd
import os

def fetch_data(ticker: str, start="2024-01-01", end=None):
    """
    Fetch stock price data from Yahoo Finance using yfinance.
    Returns a cleaned DataFrame and saves it under /data/.
    """
    print(f"📡 正在从 Yahoo Finance 获取 {ticker} 的数据...")
    data = yf.download(ticker, start=start, end=end, auto_adjust=True, progress=False)

    if data.empty:
        print("⚠️ 获取数据失败，请检查股票代码是否正确。")
        return None

    if isinstance(data.columns, pd.MultiIndex):
        data.columns = data.columns.get_level_values(0)
        
    # 只保留必要列
    data = data.reset_index()[['Date', 'Close']]

    # 确保 data 文件夹存在
    os.makedirs("data", exist_ok=True)
    filename = f"data/{ticker.upper()}.csv"
    data.to_csv(filename, index=False)

    print(f"✅ 数据获取成功，已保存到 {filename}")
    return data
