def give_investment_advice(data):
    avg = data['Daily Return'].mean() * 100
    vol = data['Daily Return'].std() * 100

    if avg > 0.5 and vol < 2:
        advice = "📈 波动低、收益稳，可考虑中期持有。"
    elif avg > 0.2 and vol > 2:
        advice = "⚡ 高波动高潜力股票，适合短线或观察入场机会。"
    elif avg < 0:
        advice = "📉 收益为负，建议暂时观望。"
    else:
        advice = "💤 收益一般，建议分散投资。"

    print(f"\n平均收益率: {avg:.2f}%")
    print(f"波动率(风险): {vol:.2f}%")
    print(f"💬 投资建议：{advice}")