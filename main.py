from data_loader import load_data
from analysis import analyze_returns
from visualize import plot_stock
from investment_advisor import give_investment_advice
from fetch_data import fetch_data
from os.path import exists



def main():
    print("📈 This is Stock Pitcher, a tool to help you analyze capital market.")

    while True:
        print("\n=== Main Menu ===")
        print("1. Load your local stock date")
        print("2. Fetch data online from Yahoo Finance")
        print("3. Exit Stock Pitcher")
        choosefunction1 = input("Please select function (1-3): ")

        if choosefunction1 == "1":
            localfilename = input("Type in file path(format: data/AAPL.csv): ").strip()
            if not exists(localfilename):
                print("❌ Cannot find it, please check the path.")
                continue
            try:
                raw_data = load_data(localfilename)
                analyzed_data = analyze_returns(raw_data)
            except Exception as error:
                print(f"⚠️ Error happens: {error}")
                continue

        elif choosefunction1 == "2":
            ticker = input("Type in one stock ticker(e.g. AAPL、TSLA、MSFT): ").strip().upper()
            raw_data = fetch_data(ticker)
            if raw_data is None:
                continue
            analyzed_data = analyze_returns(raw_data)

        elif choosefunction1 == "3":
            print("👋 Thanks for choosing <PERSON> Pitcher, See you!")
            break

        else:
            print("⚠️ Please choose between 1-3")
            continue

        while True:
            print("\n=== Analyze Menu ===")
            print("1. Trend Visualization")
            print("2. Investment advice")
            print("3. Pitch another stock")
            print("4. Back to main menu")
            choosefunction2 = input("Please select function (1-4): ")

            if choosefunction2 == "1":
                plot_stock(analyzed_data)
                continue
            elif choosefunction2 == "2":
                give_investment_advice(analyzed_data)
                continue
            elif choosefunction2 == "3":
                break 
            elif choosefunction2 == "4":
                break  
            else:
                print("⚠️ Please choose between 1-4")

if __name__ == "__main__":
    main()
