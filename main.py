# main.py
from data_loader import load_data
from analysis import analyze_returns
from visualize import plot_stock
from investment_advisor import give_investment_advice
from fetch_data import fetch_data
import os

def main():
    print("📈 欢迎使用智能投资分析助手！")

    while True:
        print("\n=== 主菜单 ===")
        print("1. 载入本地股票数据文件（CSV）")
        print("2. 在线获取股票数据（yfinance）")
        print("3. 退出程序")
        choice = input("请选择操作 (1-3)：")

        # --- 选项 1: 加载本地文件 ---
        if choice == "1":
            filename = input("请输入CSV文件路径（例如 data/AAPL.csv）：").strip()
            if not os.path.exists(filename):
                print("❌ 找不到该文件，请确认路径正确。")
                continue

            try:
                data = load_data(filename)
                data = analyze_returns(data)
            except Exception as e:
                print(f"⚠️ 加载或分析过程中出现错误: {e}")
                continue

        # --- 选项 2: 在线数据（稍后阶段再实现）---
        elif choice == "2":
            ticker = input("请输入股票代码（例如 AAPL、TSLA、MSFT）：").strip().upper()
            data = fetch_data(ticker)
            if data is None:
                continue
            data = analyze_returns(data)

        # --- 选项 3: 退出程序 ---
        elif choice == "3":
            print("👋 感谢使用，再见！")
            break

        else:
            print("⚠️ 无效输入，请重新选择。")
            continue

        # --- 二级菜单：后续操作 ---
        while True:
            print("\n=== 分析菜单 ===")
            print("1. 查看股价趋势图")
            print("2. 获取投资建议")
            print("3. 继续分析其他股票")
            print("4. 返回主菜单")
            next_choice = input("请选择操作 (1-4)：")

            if next_choice == "1":
                plot_stock(data)
                continue
            elif next_choice == "2":
                give_investment_advice(data)
                continue
            elif next_choice == "3":
                break 
            elif next_choice == "4":
                break  
            else:
                print("⚠️ 无效输入，请重新选择。")

if __name__ == "__main__":
    main()
