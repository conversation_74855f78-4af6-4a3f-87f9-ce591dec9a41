import matplotlib.pyplot as plt

def plot_stock(raw_data):
    raw_data['MA5'] = raw_data['Close'].rolling(5).mean()
    raw_data['MA20'] = raw_data['Close'].rolling(20).mean()
    #If the moving average in 5 days is trending upward and is above the moving avergae in 20 days, it generally indicates a short-term bullish trend.

    plt.figure(figsize=(10,5))
    plt.title('Stock Price with Moving Averages')
    plt.xlabel('Date')
    plt.ylabel('Price ($)')
    plt.plot(raw_data['Date'], raw_data['Close'], label='Close Price', linewidth=1)
    plt.plot(raw_data['Date'], raw_data['MA5'], label='MA5')
    plt.plot(raw_data['Date'], raw_data['MA20'], label='MA20')

    plt.legend()
    plt.show()