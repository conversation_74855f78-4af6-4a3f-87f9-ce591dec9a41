import matplotlib.pyplot as plt

def plot_stock(data):
    """Plots closing price and moving averages."""
    data['MA5'] = data['Close'].rolling(5).mean()
    data['MA20'] = data['Close'].rolling(20).mean()

    plt.figure(figsize=(10,5))
    plt.plot(data['Date'], data['Close'], label='Close Price', linewidth=1.5)
    plt.plot(data['Date'], data['MA5'], label='MA5')
    plt.plot(data['Date'], data['MA20'], label='MA20')
    plt.title('Stock Price with Moving Averages')
    plt.xlabel('Date')
    plt.ylabel('Price ($)')
    plt.legend()
    plt.show()